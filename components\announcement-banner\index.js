// 公告横幅组件
Component({
  properties: {
    // 公告列表
    announcements: {
      type: Array,
      value: []
    },
    // 是否显示关闭按钮
    showCloseButton: {
      type: Boolean,
      value: false
    }
  },

  data: {
    showModal: false,
    selectedAnnouncement: {},
    currentIndex: 0,
    isTransitioning: false
  },

  methods: {
    // swiper变化事件
    onSwiperChange(e) {
      const { current, source } = e.detail;
      console.log('📢 [公告滚动] swiper变化:', { current, source });

      this.setData({
        currentIndex: current
      });
    },

    // swiper过渡事件
    onSwiperTransition(e) {
      const { dx, dy } = e.detail;
      // 标记正在过渡中
      this.setData({
        isTransitioning: true
      });
    },

    // swiper动画完成事件
    onSwiperAnimationFinish(e) {
      const { current } = e.detail;
      console.log('📢 [公告滚动] 动画完成:', current);

      // 标记过渡完成
      this.setData({
        isTransitioning: false,
        currentIndex: current
      });
    },

    // 点击公告项
    onAnnouncementTap(e) {
      const announcement = e.currentTarget.dataset.announcement;
      if (!announcement) return;

      console.log('点击公告:', announcement.title);

      // 更新查看次数
      this.updateViewCount(announcement._id);

      // 格式化时间显示
      const formatTime = (timeStr) => {
        if (!timeStr) return '';
        try {
          const date = new Date(timeStr);
          return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
          });
        } catch (error) {
          return timeStr;
        }
      };

      // 准备弹窗数据
      const modalData = {
        ...announcement,
        publishTimeFormatted: formatTime(announcement.publishTime),
        expireTimeFormatted: formatTime(announcement.expireTime)
      };

      // 显示弹窗
      this.setData({
        showModal: true,
        selectedAnnouncement: modalData
      });

      // 触发自定义事件
      this.triggerEvent('announcementTap', {
        announcement: announcement
      });
    },

    // 关闭横幅
    onCloseBanner() {
      console.log('关闭公告横幅');

      // 触发自定义事件
      this.triggerEvent('closeBanner');
    },

    // 关闭弹窗
    onCloseModal() {
      console.log('关闭公告弹窗');

      this.setData({
        showModal: false,
        selectedAnnouncement: {}
      });
    },

    // 更新查看次数
    async updateViewCount(announcementId) {
      try {
        await wx.cloud.callFunction({
          name: 'announcementManager',
          data: {
            action: 'updateViewCount',
            announcementId: announcementId
          }
        });
      } catch (error) {
        console.error('更新公告查看次数失败:', error);
      }
    }
  }
});
