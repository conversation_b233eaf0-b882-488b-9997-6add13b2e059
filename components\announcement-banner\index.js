// 公告横幅组件
Component({
  properties: {
    // 公告列表
    announcements: {
      type: Array,
      value: [],
      observer: function(newVal, oldVal) {
        // 当公告列表变化时，重新启动自动循环
        if (newVal && newVal.length > 0) {
          this.setData({
            currentIndex: 0
          });
          // 延迟启动，确保DOM已更新
          setTimeout(() => {
            this.startAutoLoop();
          }, 100);
        } else {
          this.clearAutoLoop();
        }
      }
    },
    // 是否显示关闭按钮
    showCloseButton: {
      type: Boolean,
      value: false
    }
  },

  data: {
    showModal: false,
    selectedAnnouncement: {},
    currentIndex: 0
  },

  lifetimes: {
    attached() {
      // 组件初始化时启动自动循环
      this.startAutoLoop();
    },

    detached() {
      // 组件销毁时清除定时器
      this.clearAutoLoop();
    }
  },

  methods: {
    // 启动自动循环
    startAutoLoop() {
      this.clearAutoLoop();

      if (this.data.announcements.length <= 1) {
        return;
      }

      this.autoLoopTimer = setInterval(() => {
        const currentIndex = this.data.currentIndex;
        const nextIndex = (currentIndex + 1) % this.data.announcements.length;

        this.setData({
          currentIndex: nextIndex
        });

        // 使用swiper的swipeTo方法平滑切换
        const swiperContext = this.selectComponent('.announcement-swiper');
        if (swiperContext && swiperContext.swipeTo) {
          swiperContext.swipeTo(nextIndex);
        }
      }, 4000);
    },

    // 清除自动循环
    clearAutoLoop() {
      if (this.autoLoopTimer) {
        clearInterval(this.autoLoopTimer);
        this.autoLoopTimer = null;
      }
    },

    // swiper变化事件
    onSwiperChange(e) {
      const { current } = e.detail;
      this.setData({
        currentIndex: current
      });
    },

    // 点击公告项
    onAnnouncementTap(e) {
      const announcement = e.currentTarget.dataset.announcement;
      if (!announcement) return;

      console.log('点击公告:', announcement.title);

      // 更新查看次数
      this.updateViewCount(announcement._id);

      // 格式化时间显示
      const formatTime = (timeStr) => {
        if (!timeStr) return '';
        try {
          const date = new Date(timeStr);
          return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
          });
        } catch (error) {
          return timeStr;
        }
      };

      // 准备弹窗数据
      const modalData = {
        ...announcement,
        publishTimeFormatted: formatTime(announcement.publishTime),
        expireTimeFormatted: formatTime(announcement.expireTime)
      };

      // 显示弹窗
      this.setData({
        showModal: true,
        selectedAnnouncement: modalData
      });

      // 触发自定义事件
      this.triggerEvent('announcementTap', {
        announcement: announcement
      });
    },

    // 关闭横幅
    onCloseBanner() {
      console.log('关闭公告横幅');

      // 触发自定义事件
      this.triggerEvent('closeBanner');
    },

    // 关闭弹窗
    onCloseModal() {
      console.log('关闭公告弹窗');

      this.setData({
        showModal: false,
        selectedAnnouncement: {}
      });
    },

    // 更新查看次数
    async updateViewCount(announcementId) {
      try {
        await wx.cloud.callFunction({
          name: 'announcementManager',
          data: {
            action: 'updateViewCount',
            announcementId: announcementId
          }
        });
      } catch (error) {
        console.error('更新公告查看次数失败:', error);
      }
    }
  }
});
